.restaurant-list {
  width: 70%;
  margin-left: 5vw;
  margin-top: 50px;
  color: #6d6d6d;
  font-size: 16px;
}

.restaurant-list-header {
  margin-bottom: 30px;
}

.restaurant-list-header h2 {
  color: #333;
  font-size: 24px;
  margin-bottom: 8px;
}

.restaurant-list-header p {
  color: #666;
  font-size: 14px;
}

.restaurant-list-table {
  border: 1px solid #cacaca;
  border-radius: 8px;
  overflow: hidden;
}

.restaurant-list-table-format {
  display: grid;
  grid-template-columns: 0.5fr 1.5fr 2fr 2fr 1fr 0.8fr 0.5fr;
  align-items: center;
  gap: 10px;
  padding: 12px 15px;
  border-bottom: 1px solid #cacaca;
  font-size: 13px;
}

.restaurant-list-table-format.title {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.restaurant-list-table-format:last-child {
  border-bottom: none;
}

.restaurant-list-table-format img {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 6px;
}

.restaurant-list-table-format p {
  margin: 0;
  line-height: 1.4;
}

.restaurant-list-table-format .description {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 12px;
}

.restaurant-list-table-format .address {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 12px;
}

.delivery-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.delivery-info p {
  font-size: 11px;
  margin: 0;
}

.rating {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.cursor {
  cursor: pointer;
  font-size: 16px;
  text-align: center;
  transition: transform 0.2s;
}

.cursor:hover {
  transform: scale(1.2);
}

.no-restaurants {
  text-align: center;
  padding: 60px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.no-restaurants p {
  font-size: 16px;
  color: #666;
  margin: 0;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .restaurant-list {
    width: 90%;
    margin-left: 5%;
    margin-top: 30px;
  }
  
  .restaurant-list-table-format {
    grid-template-columns: 1fr;
    gap: 8px;
    padding: 15px;
    text-align: left;
  }
  
  .restaurant-list-table-format.title {
    display: none;
  }
  
  .restaurant-list-table-format {
    border-bottom: 2px solid #e9ecef;
    background: white;
    margin-bottom: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .restaurant-list-table-format img {
    width: 80px;
    height: 80px;
    margin-bottom: 10px;
  }
  
  .restaurant-list-table-format p {
    margin-bottom: 5px;
  }
  
  .restaurant-list-table-format .description,
  .restaurant-list-table-format .address {
    -webkit-line-clamp: 3;
    font-size: 13px;
  }
  
  .delivery-info {
    flex-direction: row;
    gap: 10px;
  }
  
  .cursor {
    align-self: flex-end;
    margin-top: 10px;
  }
}
