.header{
    height:34vw;
    margin:30px auto;
    background: url('../../../assets/header_img2.png') no-repeat center center;
    background-size: cover;
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    min-height: 300px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}
.header-contents{
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: start;
    gap: 1.5vw;
    max-width: 50%;
    bottom: 10%;
    left: 6vw;
    animation: fadeIn 3s;
}

.header-contents h2{
    font-weight: 500;
    color: white;
    font-size: max(4.5vw,22px);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.header-contents p{
    color: white;
    font-size: 1.5vw;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

.header-contents button{
    border: none;
    color: #747474;
    font-weight: 500;
    padding: 1vw 2.3vw;
    background-color: white;
    font-size: max(1vw,13px);
    border-radius: 50px;
    cursor: pointer; /* Add pointer cursor for better UX */
    transition: all 0.3s ease; /* Smooth transition for hover effects */
}

.header-contents button:hover {
    background-color: #f0f0f0; /* Slightly darker background on hover */
    color: #333; /* Change text color on hover */
    transform: scale(1.05); /* Slightly enlarge button on hover */
}

.header-contents button:active {
    transform: scale(0.95); /* Slightly shrink button when clicked */
    background-color: #e0e0e0; /* Darker background on click */
}
@media (max-width:1050px) {
    .header-contents{
        max-width: 45%;
    }
}
@media (max-width:750px) {
    .header{
        height: 40vw;
        min-height: 250px;
        margin: 20px auto;
        background-size: cover;
        background-position: center;
    }
    .header-contents{
        max-width: 70%;
        padding: 2vw 4vw;
        bottom: 15%;
    }
    .header-contents p{
        display: none;
    }
    .header-contents h2{
        font-size: max(5vw, 20px);
        line-height: 1.2;
    }
    .header-contents button{
        padding: 2vw 3vw;
        font-size: max(1.2vw, 14px);
    }
}

@media (max-width: 480px) {
    .header{
        height: 45vw;
        min-height: 220px;
        margin: 15px auto;
        border-radius: 8px;
    }
    .header-contents{
        max-width: 80%;
        bottom: 20%;
        left: 4vw;
        gap: 1vw;
    }
    .header-contents h2{
        font-size: max(4.5vw, 18px);
        line-height: 1.1;
    }
    .header-contents button{
        padding: 2.5vw 4vw;
        font-size: max(1.5vw, 12px);
        border-radius: 25px;
    }
}

@media (max-width: 320px) {
    .header{
        height: 50vw;
        min-height: 200px;
        margin: 10px auto;
    }
    .header-contents{
        max-width: 85%;
        bottom: 25%;
        left: 3vw;
    }
    .header-contents h2{
        font-size: 16px;
        line-height: 1.1;
    }
    .header-contents button{
        padding: 8px 16px;
        font-size: 11px;
    }
}