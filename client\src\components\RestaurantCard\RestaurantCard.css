.restaurant-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  min-width: 280px;
  max-width: 280px;
  flex-shrink: 0;
}

.restaurant-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.restaurant-image {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
}

.restaurant-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.restaurant-card:hover .restaurant-image img {
  transform: scale(1.05);
}

.delivery-time {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.restaurant-info {
  padding: 8px;
}

.restaurant-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.restaurant-description {
  font-size: 11px;
  color: #666;
  margin: 0 0 6px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.restaurant-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.rating {
  display: flex;
  align-items: center;
  gap: 4px;
}

.rating-star {
  font-size: 14px;
}

.rating-value {
  font-size: 13px;
  font-weight: 600;
  color: #333;
}

.delivery-info {
  font-size: 11px;
  color: #666;
}

.delivery-fee {
  font-weight: 500;
}

.cuisine-types {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 6px;
}

.cuisine-tag {
  background: #f8f9fa;
  color: #666;
  padding: 3px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
  border: 1px solid #e9ecef;
}

.cuisine-tag.more {
  background: #e9ecef;
  color: #495057;
}

.minimum-order {
  font-size: 11px;
  color: #666;
  font-weight: 500;
}

/* Tablet responsive */
@media (max-width: 1024px) {
  .restaurant-card {
    margin-bottom: 14px;
  }

  .restaurant-image {
    height: 150px;
  }

  .restaurant-info {
    padding: 12px;
  }

  .restaurant-name {
    font-size: 15px;
  }

  .restaurant-description {
    font-size: 12px;
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .restaurant-card {
    min-width: 240px;
    max-width: 240px;
    border-radius: 10px;
  }

  .restaurant-image {
    height: 140px;
  }

  .restaurant-info {
    padding: 10px;
  }

  .restaurant-name {
    font-size: 14px;
    margin-bottom: 4px;
  }

  .restaurant-description {
    font-size: 11px;
    -webkit-line-clamp: 1;
    margin-bottom: 6px;
  }

  .restaurant-details {
    margin-bottom: 6px;
  }

  .rating-value {
    font-size: 12px;
  }

  .delivery-info {
    font-size: 11px;
  }

  .cuisine-tag {
    font-size: 10px;
    padding: 3px 6px;
  }

  .minimum-order {
    font-size: 11px;
  }

  .delivery-time {
    font-size: 10px;
    padding: 3px 6px;
  }
}

/* Small mobile responsive */
@media (max-width: 480px) {
  .restaurant-card {
    min-width: 200px;
    max-width: 200px;
    border-radius: 8px;
  }

  .restaurant-image {
    height: 120px;
  }

  .restaurant-info {
    padding: 6px;
  }

  .restaurant-name {
    font-size: 13px;
    margin-bottom: 3px;
  }

  .restaurant-description {
    font-size: 10px;
    margin-bottom: 4px;
  }

  .restaurant-details {
    margin-bottom: 4px;
    gap: 8px;
  }

  .rating-value {
    font-size: 11px;
  }

  .delivery-info {
    font-size: 10px;
  }

  .cuisine-types {
    gap: 4px;
    margin-bottom: 4px;
  }

  .cuisine-tag {
    font-size: 9px;
    padding: 2px 4px;
  }

  .minimum-order {
    font-size: 10px;
  }

  .delivery-time {
    font-size: 9px;
    padding: 2px 4px;
  }
}
